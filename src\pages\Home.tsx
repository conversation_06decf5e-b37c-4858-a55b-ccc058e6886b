import { Link } from "react-router-dom";

export default function Home() {
  return (
    <div className="w-full h-full bg-[url('/img/rideaux-rouge.gif')] bg-no-repeat bg-center bg-cover flex justify-center items-center">

      {/* :FIXME: TO BE DELETED */}
      <nav className='absolute top-[0px] left-[0px] px-[6px] py-[4px]' style={{ display: 'flex', gap: '10px' }}>
        <Link to="/">Home</Link>
        <Link to="/bienvenue">Bienvenue</Link>
        <Link to="/instructions">instructions</Link>
        <Link to="/perdu">Perdu</Link>
        <Link to="/reussi">Reussi</Link>
        <Link to="/jackpot">jackpot</Link>
      </nav>

      <div className="absolute bottom-[0px] left-[0px] text-[26px] font-[600] text-[#FFF] px-[6px] py-[0px]">Erreur : Accès refusé - badge non autorisé</div>

    </div>
  )
}
