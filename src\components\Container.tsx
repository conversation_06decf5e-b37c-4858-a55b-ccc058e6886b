import React from 'react';
import Header from "./Header";
import Footer from "./Footer";
import { Link } from 'react-router-dom';

interface ContainerProps {
  children: React.ReactNode,
  background: string
}

export default function Container({children, background}: ContainerProps) {
  return (
    <div className="relative w-full h-full bg-no-repeat bg-center bg-cover" style={{ backgroundImage: `url(/img/${background}.png)` }}>

        <div className="w-full h-full" style={{ backgroundImage: `url(/img/text-background.png)` }}>

          <Header />

          {children}

          {/* :FIXME: TO BE DELETED */}
          <nav className='absolute top-[0px] px-[6px] py-[4px]' style={{ display: 'flex', gap: '10px' }}>
            <Link to="/">Home</Link>
            <Link to="/bienvenue">Bienvenue</Link>
            <Link to="/instructions">instructions</Link>
            <Link to="/perdu">Perdu</Link>
            <Link to="/reussi"><PERSON><PERSON>i</Link>
            <Link to="/jackpot">jackpot</Link>
          </nav>

          <Footer />

        </div>

        <div className="absolute bottom-[0px] left-[0px] text-[26px] font-[600] text-[#FFF] px-[6px] py-[0px]">Accès refusé - badge non autorisé</div>

    </div>
  )
}
