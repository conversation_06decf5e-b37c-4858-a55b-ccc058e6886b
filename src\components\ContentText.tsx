import type React from "react"

interface ContentProps {
  children: React.ReactNode,
  ticket: string,
  personnage: string,
  title: string,
  timer: string,
  status: string
}

function ContentSimple(children: React.ReactNode, title: string, timer: string) {
  return (
    <div className="h-[426px] w-[730px] absolute top-[272px] left-[282px] pl-[50px] py-[50px] flex flex-col items-center justify-between">

      <div className="space-y-[10px]">
          <div className="font-alfa text-[75px] text-[#CB0228] capitalize">{title}</div>
          <div className="font-inter text-[#0C5394] font-[600] space-y-[10px]">
            {children}
          </div>
      </div>

      <div className="w-[285px] h-[115px] font-alfa text-[#FFF] flex flex-col justify-center bg-[url(/img/time-background.png)] bg-no-repeat bg-center bg-cover">
          <div className="text-[40px] mb-[15px] drop-shadow-[0_4px_1px_rgba(0,0,0,1)]">{timer} min</div>
      </div>

    </div>
  );
}

function ContentJackpot(children: React.ReactNode, title: string) {
  return (
    <div className="h-[426px] w-[730px] absolute top-[272px] left-[282px] pl-[50px] py-[50px] flex flex-col items-center justify-center">
        <div className="space-y-[10px]">
            <div className="font-alfa text-[165px] text-[#CB0228] capitalize">{title}</div>
            <div className="font-inter text-[#CB0228] font-[600] space-y-[10px]">
              {children}
            </div>
        </div>
    </div>
  );
}

export default function ContentText({children, ticket, personnage, title, timer, status}: ContentProps) {
  return (
    <div className="w-full h-full" style={{ backgroundImage: `url(/img/${ticket})` }}>
      
      <div className="w-full h-full grid content-center absolute top-[0px] left-[0px]" style={{ backgroundImage: `url(/img/personnage-${personnage}.png)` }}>

          {(() => {
            switch (status) {

              case 'SIMPLE':
                return ContentSimple(children, title, timer);

              case 'JACKPOT':
                return ContentJackpot(children, title);

              default:
                return null;
            }
          })()}
          
      </div>

    </div>
  )
}
