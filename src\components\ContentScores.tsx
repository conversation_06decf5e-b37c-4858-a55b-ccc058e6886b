import type React from "react"

interface ContentProps {
  children: React.ReactNode,
  ticket: string,
  personnage: string,
  description: string,
  score: number
}

export default function ContentScores({ children, ticket, personnage, description, score }: ContentProps) {
  return (
    <div className="w-full h-full overflow-hidden" style={{ backgroundImage: `url(/img/${ticket})` }}>

      <div className="w-full h-full grid content-center absolute top-[0px] left-[0px] overflow-hidden" style={{ backgroundImage: `url(/img/personnage-${personnage}.png)` }}>

        <div className="grid content-center h-[526px] w-[472px] absolute top-[272px] left-[282px]">
          <div className="min-h-[355px] py-[15px]">
            <div className="mb-[20px]">
              <div className="font-alfa text-[60px] leading-[65px] text-[#CB0228]">Scores</div>
              <div className="font-alfa text-[35px] text-[#CB0228]">Individuels</div>
            </div>
            {children}
          </div>
        </div>
        <div className="grid content-center space-y-[20px] h-[526px] w-[442px] absolute top-[272px] right-[277px]">
          <div className="flex flex-col items-center space-y-[20px]">
            <div className="font-alfa text-[38px] leading-[40px] text-[#CB0228]">Score<br />de l'épreuve</div>
            <div className="font-alfa text-[#0C5394] text-[34px] uppercase">{description}</div>
            <div className="w-[294px] h-[110px] font-alfa text-[#FFF] flex flex-col justify-center bg-[url(/img/score-background.png)] bg-no-repeat bg-center bg-cover">
              <div className="text-[38px] mb-[14px] drop-shadow-[0_4px_1px_rgba(0,0,0,1)]">{new Intl.NumberFormat('fr-FR').format(score)}</div>
            </div>
          </div>
        </div>

      </div>

    </div>
  )
}