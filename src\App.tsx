import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import Bienvenue from './pages/Bienvenue';
import Instructions from './pages/Instructions';
import Perdu from './pages/Perdu';
import <PERSON>uss<PERSON> from './pages/Reussi';
import Jackpot from './pages/Jackpot';

function App() {

  return (
    <div className="w-screen-fixed h-screen-fixed max-w-screen-fixed max-h-screen-fixed overflow-hidden relative">
      <Router>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/bienvenue" element={<Bienvenue />} />
          <Route path="/instructions" element={<Instructions />} />
          <Route path="/perdu" element={<Perdu />} />
          <Route path="/reussi" element={<Reussi />} />
          <Route path="/jackpot" element={<Jackpot />} />
        </Routes>
      </Router>
    </div>
  )
}

export default App
