import Container from "../components/Container";
import ContentScores from "../components/ContentScores";

export default function <PERSON><PERSON><PERSON>() {
  return (
    <Container background='rideaux-rouge'>
      <ContentScores ticket='ticket-simple.png' personnage='reussi' description="Félicitations !" score={10000}>
        <div className="space-y-[20px] ml-[90px] text-left font-alfa text-[#0C5394] text-[25px] uppercase">
          <div className="">1<sup className="text-[14px]">er</sup> Pseudo 2 : 1 000</div>
          <div className="">2<sup className="text-[14px]">eme</sup> Pseudo 1 : 1 000</div>
          <div className="">3<sup className="text-[14px]">eme</sup> Pseudo 3 : 1 000</div>
          <div className="">4<sup className="text-[14px]">eme</sup> Pseudo 4 : 1 000</div>
        </div>
      </ContentScores>
    </Container>
  )
}
