import Container from "../components/Container";
import ContentScores from "../components/ContentScores";

export default function Perdu() {
  return (
    <Container background='rideaux-rouge'>
      <ContentScores ticket='ticket-simple.png' personnage='perdu' description="Insuffisant..." score={300}>
        <div className="space-y-[20px] ml-[120px] text-left font-alfa text-[#0C5394] text-[25px] uppercase">
          <div className="">Joueur 2 : 300</div>
          <div className="">Joueur 1 : 0</div>
          <div className="">Joueur 3 : 0</div>
          <div className="">Joueur 4 : 0</div>
        </div>
      </ContentScores>
    </Container>
  )
}
